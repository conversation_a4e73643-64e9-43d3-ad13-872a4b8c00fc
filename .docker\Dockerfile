FROM node:18-alpine AS deps
WORKDIR /app
COPY package.json ./
RUN npm install --unsafe-perm

FROM node:18-alpine AS builder
ENV NODE_ENV production
WORKDIR /app
ARG NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID
ARG NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID
ARG NEXT_PUBLIC_STRAPI_URL
ARG NEXT_PUBLIC_SITE_URL
ARG NEXT_PUBLIC__GOOGLE_TAG_MANAGER
ARG NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION
ARG NEXT_PUBLIC__ALGOLIA_APP_ID
ARG NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES
ARG NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES
ARG NEXT_PUBLIC_HUBSPOT_API_KEY
ARG NEXT_PUBLIC_HUBSPOT_PORTAL_ID
ARG NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID
ARG NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID
ARG NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL
ARG NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL
ARG NEXT_PUBLIC_SENDGRID_API_KEY
ARG NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID
ARG NEXT_PUBLIC_MAIL_TO
ARG NEXT_PUBLIC_MAIL_FROM
ARG NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID
ARG NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID
ENV NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID=$NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID
ENV NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID=$NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID
ENV NEXT_PUBLIC_STRAPI_URL=$NEXT_PUBLIC_STRAPI_URL
ENV NEXT_PUBLIC_SITE_URL=$NEXT_PUBLIC_SITE_URL
ENV NEXT_PUBLIC__GOOGLE_TAG_MANAGER=$NEXT_PUBLIC__GOOGLE_TAG_MANAGER
ENV NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION=$NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION
ENV NEXT_PUBLIC__ALGOLIA_APP_ID=$NEXT_PUBLIC__ALGOLIA_APP_ID
ENV NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY=$NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES
ENV NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES
ENV NEXT_PUBLIC_HUBSPOT_API_KEY=$NEXT_PUBLIC_HUBSPOT_API_KEY
ENV NEXT_PUBLIC_HUBSPOT_PORTAL_ID=$NEXT_PUBLIC_HUBSPOT_PORTAL_ID
ENV NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID=$NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID
ENV NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID=$NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID
ENV NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL=$NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL
ENV NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL=$NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL
ENV NEXT_PUBLIC_SENDGRID_API_KEY=$NEXT_PUBLIC_SENDGRID_API_KEY
ENV NEXT_PUBLIC_MAIL_TO=$NEXT_PUBLIC_MAIL_TO
ENV NEXT_PUBLIC_MAIL_FROM=$NEXT_PUBLIC_MAIL_FROM
ENV NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID=$NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID
ENV NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID=$NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID
ENV NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID=$NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID
COPY . .
COPY src/. ./
COPY --from=deps /app/node_modules ./node_modules
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules

EXPOSE 3000
CMD ["node_modules/.bin/next", "start"]  
