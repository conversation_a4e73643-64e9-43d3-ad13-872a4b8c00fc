@value variables: "@styles/variables.module.css";
@value brandColor<PERSON>ne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.results_wrapper {
  max-width: 1192px;
  margin: 0 auto;
  padding: 40px 20px;
  text-align: center;

  @media screen and (max-width: 1192px) {
    margin: 0 36px;
  }

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 20px 16px;
  }
}

/* Restart Button */
.restart_button_wrapper {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 40px;

  @media screen and (max-width: breakpoint-xl-1024) {
    margin-bottom: 20px;
  }
}

.restart_button {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 12px 24px !important;
  border: 2px solid brandColorOne !important;
  border-radius: 3px !important;
  background-color: colorWhite !important;
  color: brandColorOne !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
}

.restart_button:hover {
  background-color: brandColorOne !important;
  color: colorWhite !important;
}

/* Main Heading */
.results_header {
  margin-bottom: 40px;

  @media screen and (max-width: breakpoint-xl-1024) {
    margin-bottom: 30px;
  }
}

.main_heading {
  font-size: 48px;
  font-weight: 700;
  color: colorBlack;
  line-height: 1.2;
  margin: 0;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 36px;
  }

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

/* Cost Display */
.cost_display {
  margin-bottom: 40px;

  @media screen and (max-width: breakpoint-xl-1024) {
    margin-bottom: 30px;
  }
}

.cost_range {
  font-size: 64px;
  font-weight: 700;
  color: #FF6B6B;
  line-height: 1.1;
  margin-bottom: 20px;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 48px;
  }

  @media screen and (max-width: breakpoint-sm) {
    font-size: 36px;
  }
}

/* Disclaimer */
.disclaimer {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 60px;
  text-align: left;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 20px;
    margin-bottom: 40px;
  }
}

.disclaimer p {
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
  color: #333;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 14px;
  }
}

/* Savings Section */
.savings_section {
  margin-bottom: 60px;

  @media screen and (max-width: breakpoint-xl-1024) {
    margin-bottom: 40px;
  }
}

.savings_heading {
  font-size: 40px;
  font-weight: 700;
  color: colorBlack;
  margin-bottom: 24px;
  line-height: 1.2;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 32px;
    margin-bottom: 20px;
  }

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

.savings_description {
  font-size: 18px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 16px;
    margin-bottom: 30px;
  }
}

/* Benefits Grid */
.benefits_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 50px;

  @media screen and (max-width: breakpoint-xl-1024) {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 40px;
  }
}

.benefit_card {
  background-color: colorWhite;
  padding: 32px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  text-align: left;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 24px 20px;
  }
}

.benefit_card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.benefit_title {
  font-size: 20px;
  font-weight: 700;
  color: colorBlack;
  margin-bottom: 16px;
  line-height: 1.3;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 18px;
    margin-bottom: 12px;
  }
}

.benefit_description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin: 0;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 14px;
  }
}

/* CTA Button */
.cta_button_wrapper {
  display: flex;
  justify-content: center;
}

.cta_button {
  background: linear-gradient(135deg, brandColorOne 0%, brandColorTwo 100%) !important;
  color: colorWhite !important;
  border: none !important;
  padding: 16px 40px !important;
  border-radius: 8px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  display: inline-block !important;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 14px 32px !important;
    font-size: 16px !important;
  }
}

.cta_button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
}
