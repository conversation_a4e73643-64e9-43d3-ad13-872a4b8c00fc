import { NextResponse } from 'next/server';
import sendDataToHubspot from 'common/sendDataToHubSpot';
import sendDataToSendGrid from 'common/sendDataToSendGrid';
import currentTimestamp from 'common/currentTimestamp';
import sendToSlack from 'common/sendDataToSlack';

export async function POST(req: Request) {
  try {
    const form_data = await req.json();

    const formFields = [
      { name: 'firstname', value: form_data?.firstName ?? '' },
      { name: 'lastname', value: form_data?.lastName ?? '' },
      { name: 'email', value: form_data?.emailAddress ?? '' },
      { name: 'company', value: form_data?.companyName ?? '' },
      { name: 'phone', value: form_data?.phoneNumber ?? '' },
      { name: 'city', value: form_data?.city ?? '' },
      { name: 'country', value: form_data?.country ?? '' },
      { name: 'ip_address', value: form_data?.ip_address ?? '' },
      { name: 'ga_4_userid', value: form_data?.ga_4_userid },
      { name: 'clarity_link', value: form_data?.clarity ?? '' },
      {
        name: 'source',
        value: form_data?.secondary_source ?? 'Cloud Migration Cost Calculator',
      },
      { name: 'source_url', value: form_data?.url ?? '' },
      { name: 'utm_campaign', value: form_data?.utm_campaign ?? '' },
      { name: 'utm_source', value: form_data?.utm_source ?? '' },
      { name: 'utm_medium', value: form_data?.utm_medium ?? '' },
      { name: 'referrer', value: form_data?.referrer ?? '' },
      { name: 'consent', value: form_data?.consent ?? '' },
      { name: 'how_can_we_help_you', value: form_data?.howCanWeHelpYou ?? '' },

      // Cloud Migration Cost Calculation Results
      {
        name: 'minimum_cost',
        value: form_data?.minimum_cost ?? form_data?.lowerRange ?? '',
      },
      {
        name: 'maximum_migration_cost',
        value: form_data?.maximum_migration_cost ?? form_data?.upperRange ?? '',
      },
      { name: 'total_estimated_cost', value: form_data?.totalCost ?? '' },

      // Cloud Migration Assessment Questions - Section 1: Business & Infrastructure Assessment
      {
        name: 'which_elements_are_you_planning_to_migrate_to_the_cloud',
        value:
          form_data?.which_elements_are_you_planning_to_migrate_to_the_cloud ??
          '',
      },
      {
        name: 'approximately_how_many_servers_do_you_intend_to_migrate',
        value:
          form_data?.approximately_how_many_servers_do_you_intend_to_migrate ??
          '',
      },
      {
        name: 'what_is_the_type_of_data_migration_you_intend_to_do',
        value:
          form_data?.what_is_the_type_of_data_migration_you_intend_to_do ?? '',
      },
      {
        name: 'what_is_your_current_it_infrastructure_setup',
        value: form_data?.what_is_your_current_it_infrastructure_setup ?? '',
      },
      {
        name: 'what_is_the_total_capacity_of_your_servers',
        value: form_data?.what_is_the_total_capacity_of_your_servers ?? '',
      },
      {
        name: 'what_is_the_current_monthly_infrastructure_cost_of_your_current_setup',
        value:
          form_data?.what_is_the_current_monthly_infrastructure_cost_of_your_current_setup ??
          '',
      },

      // Section 2: Workload & Resource Analysis
      {
        name: 'what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud',
        value: Array.isArray(
          form_data?.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud,
        )
          ? form_data.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud.join(
              ', ',
            )
          : form_data?.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud ??
            '',
      },
      {
        name: 'what_type_of_workloads_do_you_run',
        value: Array.isArray(form_data?.what_type_of_workloads_do_you_run)
          ? form_data.what_type_of_workloads_do_you_run.join(', ')
          : form_data?.what_type_of_workloads_do_you_run ?? '',
      },
      {
        name: 'what_is_the_average_cpu_and_memory_usage_of_your_workloads',
        value:
          form_data?.what_is_the_average_cpu_and_memory_usage_of_your_workloads ??
          '',
      },
      {
        name: 'do_you_require_high_availability_or_disaster_recovery_for_critical_applications',
        value:
          form_data?.do_you_require_high_availability_or_disaster_recovery_for_critical_applications ??
          '',
      },

      // Section 3: Cloud Provider & Deployment Preferences
      {
        name: 'which_cloud_provider_s_are_you_considering',
        value: form_data?.which_cloud_provider_s_are_you_considering ?? '',
      },
      {
        name: 'do_you_plan_to_use_reserved_instances_spot_instances_or_pay_as_you_go_pricing_models',
        value:
          form_data?.do_you_plan_to_use_reserved_instances_spot_instances_or_pay_as_you_go_pricing_models ??
          '',
      },
      {
        name: 'which_cloud_environments_are_you_planning_to_deploy',
        value: Array.isArray(
          form_data?.which_cloud_environments_are_you_planning_to_deploy,
        )
          ? form_data.which_cloud_environments_are_you_planning_to_deploy.join(
              ', ',
            )
          : form_data?.which_cloud_environments_are_you_planning_to_deploy ??
            '',
      },
      {
        name: 'do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_meet',
        value:
          form_data?.do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_meet ??
          '',
      },

      // Section 4: Security, Compliance & Migration Strategy
      {
        name: 'what_migration_strategy_do_you_prefer',
        value: Array.isArray(form_data?.what_migration_strategy_do_you_prefer)
          ? form_data.what_migration_strategy_do_you_prefer.join(', ')
          : form_data?.what_migration_strategy_do_you_prefer ?? '',
      },
      {
        name: 'do_you_need_auto_scaling_capabilities_for_cost_optimization',
        value:
          form_data?.do_you_need_auto_scaling_capabilities_for_cost_optimization ??
          '',
      },
      {
        name: 'how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses',
        value:
          form_data?.how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses ??
          '',
      },

      // Cost breakdown factors (for internal tracking)
      {
        name: 'server_infrastructure_cost',
        value: form_data?.costFactors?.serverCount ?? '',
      },
      {
        name: 'data_capacity_cost',
        value: form_data?.costFactors?.dataCapacity ?? '',
      },
      {
        name: 'high_availability_cost',
        value: form_data?.costFactors?.highAvailability ?? '',
      },
      {
        name: 'environments_cost',
        value: form_data?.costFactors?.environments ?? '',
      },
      {
        name: 'compliance_cost',
        value: form_data?.costFactors?.compliance ?? '',
      },
      {
        name: 'migration_strategy_cost',
        value: form_data?.costFactors?.migrationStrategy ?? '',
      },
      {
        name: 'auto_scaling_cost',
        value: form_data?.costFactors?.autoScaling ?? '',
      },
    ];

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_HUBSPOT_API_KEY}`,
      },
    };

    try {
      //Send Data to HubSpot
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        process.env.NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID,
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Send Data to SendGrid if HubSpot submission is successful
        const emailRes = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID,
          form_data,
        );

        //Send Data to success slack channel
        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
        );

        //NECESSARY LOGS -> FOR DEBUGGING PURPOSE
        console.log(currentTimestamp());
        console.log('Cloud Migration Lead Data', form_data);
        console.log('HubSpot Response', hubspotResponse);
        console.log('SendGrid Email Response', emailRes);

        return NextResponse.json(
          {
            message: 'Form submitted successfully.',
            hubspotResponse: hubspotResponse.message,
          },
          { status: 200 },
        );
      } else {
        console.error('HubSpot Error:', hubspotResponse);

        //If HubSpot POST API fails -> send failure email
        let formLeadData = form_data;
        formLeadData.page_name = form_data?.secondary_source;
        formLeadData.failed_source = 'Hubspot';

        const failureEmail = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
          formLeadData,
        );

        // If HubSpot submission fails -> Send to a failure slack channel
        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
          '⚠️ HubSpot Form Submission Failed ⚠️',
        );

        //Check if failure mail successfully sent or not
        if (failureEmail.status) {
          console.error(
            `${form_data?.secondary_source} form, failure email send`,
          );
        } else {
          console.error(
            `${form_data?.secondary_source} form, failed to send failure email`,
          );
        }

        return NextResponse.json(
          {
            message: 'Form submission failed.',
            error: hubspotResponse?.error || 'Unknown error from HubSpot',
          },
          { status: hubspotResponse?.status || 500 },
        );
      }
    } catch (error) {
      console.error('Error sending to HubSpot:', error);
      return NextResponse.json(
        {
          message: 'Internal server error while sending data to HubSpot',
          error: error.message || error,
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error parsing request:', error);
    return NextResponse.json(
      { message: 'Invalid request data', error: error.message || error },
      { status: 400 },
    );
  }
}
