// import Insights from './Insights';
// import { InsightsTypes } from './types';

// const storyData: InsightsTypes = {
//   data: {
//     id: 1,
//     title: 'Let there be change',
//     subtitle:
//       '<p>Every day, we embrace change and create value for all our stakeholders, in every part of the world.</p>',
//     taglineUrl: '#',
//     circular_text_image: {
//       data: {
//         id: 178,
//         attributes: {
//           name: 'Group 26.svg',
//           alternativeText: null,
//           caption: null,
//           width: 120,
//           height: 120,
//           formats: null,
//           hash: 'Group_26_348828df5c',
//           ext: '.svg',
//           mime: 'image/svg+xml',
//           size: 13.31,
//           url: 'https://cdn.marutitech.com/Group_26_348828df5c.svg',
//           previewUrl: null,
//           provider:
//             '@strapi-community/strapi-provider-upload-google-cloud-storage',
//           provider_metadata: null,
//           createdAt: '2024-10-01T05:09:19.099Z',
//           updatedAt: '2024-10-01T05:09:19.099Z',
//         },
//       },
//     },
//     insightsSlider: [
//       {
//         id: 1,
//         sliderTitle:
//           "Technology in the Legal  Profession: ChatGPT's  Use Cases and Challenges",
//         sliderDescription:
//           '<p>As outer space gets more crowded and contested, QuSecure is protecting communications and data transmission with the agility required for the world of post-quantum computing.</p>',
//         viewMoreUrl: '#',
//         sliderImage: {
//           data: {
//             id: 38,
//             attributes: {
//               name: 'bg-image.jpeg',
//               alternativeText: null,
//               caption: null,
//               width: 1920,
//               height: 617,
//               formats: null,
//               hash: 'bg_image_c059f3b63d',
//               ext: '.jpeg',
//               mime: 'image/jpeg',
//               size: 163.68,
//               url: 'https://cdn.marutitech.com/bg_image_c059f3b63d.jpeg',
//               previewUrl: null,
//               provider:
//                 '@strapi-community/strapi-provider-upload-google-cloud-storage',
//               provider_metadata: null,
//               createdAt: '2024-06-18T09:47:13.265Z',
//               updatedAt: '2024-06-18T09:47:13.265Z',
//             },
//           },
//         },
//       },
//       {
//         id: 2,
//         sliderTitle:
//           "Technology in the Legal  Profession: ChatGPT's  Use Cases and Challenges",
//         sliderDescription:
//           '<p>Every day, we embrace change and create value for all our stakeholders, in every part of the world.</p>',
//         viewMoreUrl: '#',
//         sliderImage: {
//           data: {
//             id: 32,
//             attributes: {
//               name: '1_carousel.webp',
//               alternativeText: null,
//               caption: null,
//               width: 1441,
//               height: 811,
//               formats: null,
//               hash: '1_carousel_ec0f64540a',
//               ext: '.webp',
//               mime: 'image/webp',
//               size: 44.61,
//               url: 'https://cdn.marutitech.com/1_carousel_ec0f64540a.webp',
//               previewUrl: null,
//               provider:
//                 '@strapi-community/strapi-provider-upload-google-cloud-storage',
//               provider_metadata: null,
//               createdAt: '2024-06-11T09:53:59.228Z',
//               updatedAt: '2024-06-27T06:05:10.634Z',
//             },
//           },
//         },
//       },
//       {
//         id: 3,
//         sliderTitle:
//           "Technology in the Legal  Profession: ChatGPT's  Use Cases and Challenges",
//         sliderDescription:
//           '<p>Every day, we embrace change and create value for all our stakeholders, in every part of the world.</p>',
//         viewMoreUrl: '#',
//         sliderImage: {
//           data: {
//             id: 51,
//             attributes: {
//               name: 'service1.jpg',
//               alternativeText: null,
//               caption: null,
//               width: 784,
//               height: 522,
//               formats: null,
//               hash: 'service1_e0695fa866',
//               ext: '.jpg',
//               mime: 'image/jpeg',
//               size: 63.72,
//               url: 'https://cdn.marutitech.com/service1_e0695fa866.jpg',
//               previewUrl: null,
//               provider:
//                 '@strapi-community/strapi-provider-upload-google-cloud-storage',
//               provider_metadata: null,
//               createdAt: '2024-06-19T12:26:28.513Z',
//               updatedAt: '2024-06-27T06:06:05.255Z',
//             },
//           },
//         },
//       },
//     ],
//   },
// };

// export default {
//   title: 'Components/Insights',
// };

// export function IndustriesCardStory() {
//   return (
//     <div>
//       <Insights data={storyData.data} />
//     </div>
//   );
// }
